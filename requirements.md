# 需求文档

## 介绍

团队语音聊天工具是一个专为游戏团队设计的轻量级语音通讯应用。该工具旨在解决现有语音聊天软件（如YY）资源占用过多的问题，为用户提供简洁、高效的语音聊天服务。其核心设计原则是**完全匿名**和**免注册**，用户无需创建账户即可立即使用。

该工具采用CS架构，支持最多16人同时在线，主要服务于游戏场景下的实时语音交流需求。

## 客户端需求

### 需求 1：快速连接

**用户故事：** 作为游戏玩家，我希望能够快速连接到语音聊天服务，这样我就可以立即与朋友开始语音交流而无需复杂的设置过程。

#### 验收标准

1. 当用户首次启动客户端时，系统应当提供输入服务器地址的界面。
2. 当用户再次启动客户端时，系统应当显示上次使用的服务器地址并允许修改。
3. 当用户点击连接按钮时，系统应当尝试连接到指定服务器。
4. 当连接成功后，系统应当自动将用户加入到全局语音房间。
5. 当用户成功连接后，系统应当保存服务器地址供下次使用。

### 需求 2：全局快捷键

**用户故事：** 作为游戏玩家，我希望能够通过快捷键控制我的麦克风状态，这样我就可以在游戏过程中快速开启或关闭麦克风而不影响游戏操作。

#### 验收标准

1. 系统需支持全局快捷键，即使在其他应用程序获得焦点时也能响应。
2. **切换麦克风状态**的快捷键设置为：
   - **Windows:** `Ctrl+Alt+T`
   - **macOS:** `Control+Option+T` (`⌃⌥T`)
3. **切换音频输出静音**的快捷键设置为：
   - **Windows:** `Ctrl+Alt+M`
   - **macOS:** `Control+Option+M` (`⌃⌥M`)
4. 当麦克风或音频输出状态改变时，系统应当在界面上显示当前状态。

### 需求 3：低资源占用

**用户故事：** 作为游戏玩家，我希望语音聊天工具占用最少的系统资源，这样我就可以在运行游戏的同时保持流畅的语音通讯。

#### 验收标准

1. 当应用程序运行时，CPU占用率应当保持在较低水平（正常情况下低于5%）。
2. 当应用程序运行时，内存占用应当保持在合理范围内（低于100MB）。
3. 当用户最小化应用程序时，系统应当减少不必要的界面渲染以节省资源。
4. 当没有语音活动时，系统应当降低处理频率以节省CPU资源。

### 需求 4：稳定的音频流

**用户故事：** 作为游戏玩家，我希望语音聊天能提供持续稳定的音频流，这样我就可以在游戏过程中进行清晰、不间断的交流。

#### 验收标准

1. 系统应采用高效的音频编解码器（如Opus），以在保证音质的同时最小化带宽占用。
2. 在正常的网络条件下，系统应提供清晰、高质量的语音传输。
3. 系统应内置合理的缓冲机制（Jitter Buffer）来应对轻微的网络波动，以保证语音流的连续性。
4. 系统应优先保证连接的稳定性，避免因网络问题导致频繁断线。

### 需求 5：简洁的控制界面

**用户故事：** 作为游戏玩家，我希望有一个简洁的控制界面，这样我就可以快速查看和控制语音聊天状态而不被复杂的功能干扰。

#### 验收标准

1. 在 **Windows** 上，应用程序应在**系统托盘 (System Tray)** 中显示一个图标。
2. 在 **macOS** 上，应用程序应在**菜单栏 (Menu Bar)** 中显示一个图标。
3. 当用户点击该图标时，系统应当弹出一个小型控制界面。
4. 弹出界面应当显示当前在线用户数量，并在用户加入或离开时实时更新。
5. 当用户的麦克风或音频状态改变时，图标和弹出界面应当实时反映这些变化。

### 需求 6：自动化的房间管理

**用户故事：** 作为游戏玩家，我希望语音房间能够自动管理，这样我就不需要手动创建或删除房间。

#### 验收标准

1. 当第一个用户连接到服务器时，系统应当自动创建全局语音房间。
2. 当用户加入房间时，系统应当将其添加到当前活跃的语音会话中。
3. 当所有用户都离开房间时，系统应当自动销毁该房间以释放服务器资源。
4. 当房间人数达到16人上限时，系统应当拒绝新用户加入并提示房间已满。





## 服务端需求

### 需求 7：命令行部署与运行

**用户故事：** 作为服务器管理员，我希望能够轻松地在我的Linux服务器上部署和运行语音聊天工具，这样我的游戏团队就可以连接上来进行交流。

#### 验收标准

1. 服务端程序必须能作为命令行应用在Linux环境下运行。
2. 服务端应能通过命令行参数进行配置（例如：监听的端口号、最大连接数）。
3. 服务端必须正确处理客户端的连接和断开请求。
4. 服务端负责接收一个客户端的音频数据，并将其转发给所有其他连接的客户端。
5. 服务端应在控制台（标准输出）打印关键日志信息，如服务器启动、客户端连接/断开、错误等。
6. 服务端应遵循“单一全局房间”的逻辑，在第一个用户连接时创建会话，在最后一个用户离开时销毁会话。