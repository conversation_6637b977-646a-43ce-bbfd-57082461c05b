# 开发任务计划

本文档概述了 TeamTalk 项目的开发任务，并将其分解为多个阶段。每个阶段在进入下一阶段前，都需要经过用户审批。

---

### 阶段一：可视化原型 (UI骨架) - Electron 客户端

**目标：** 创建一个可运行但无实际功能的 Electron 应用程序，用于展示最终的用户界面以供审查。

- [ ] **任务 1.1: 搭建项目结构**
  - [ ] 创建 `teamtalk-client` 根目录。
  - [ ] 初始化 Electron 项目（例如，使用 `electron-forge` 或手动配置 `package.json`）。
  - [ ] 配置 TypeScript 和 React/Vite。
  - [ ] 创建 `src/main` 目录用于 Electron 主进程代码。
  - [ ] 创建 `src/renderer` 目录用于 React UI 渲染进程代码。
  - [ ] 添加初始配置文件 (`tsconfig.json`, `electron-forge.config.js` 或类似)。

- [ ] **任务 1.2: 安装依赖**
  - [ ] 运行 `npm install` 来获取所有必需的软件包。

- [ ] **任务 1.3: 构建静态UI组件**
  - [ ] 在 `src/renderer/App.tsx` 中实现主应用布局。
  - [ ] 添加用于输入服务器地址的输入框。
  - [ ] 添加一个“连接”按钮。
  - [ ] 添加用于显示连接状态的文本标签。
  - [ ] 添加用于显示在线用户数的文本标签。
  - [ ] 添加图标以表示麦克风和音频输出的静音状态。
  - [ ] **单元测试:** 编写测试以验证UI组件的正确渲染。

- [ ] **任务 1.4: 应用基础样式**
  - [ ] 创建并应用 CSS 规则，以确保布局简洁、直观。

- [ ] **任务 1.5: 验证构建**
  - [ ] 运行 `npm start` 或 `npm run dev` 来确认 Electron 应用程序窗口能够正常打开并显示静态UI。

- [ ] **审批节点 1: 用户审查并批准视觉设计和布局。**

---

### 阶段二：客户端逻辑与系统集成 - Electron 客户端

**目标：** 在不进行网络通信的情况下，实现所有本地功能，使客户端“活起来”。

- [ ] **任务 2.1: 实现系统托盘/菜单栏**
  - [ ] 在 Electron 主进程中编写 TypeScript 代码，以在系统托盘 (Windows) 和菜单栏 (macOS) 中创建和管理应用程序图标。
  - [ ] 实现点击图标时显示/隐藏主窗口的逻辑。
  - [ ] **单元测试:** 为托盘/菜单栏的后台状态管理逻辑编写测试。

- [ ] **任务 2.2: 实现全局快捷键**
  - [ ] 在 Electron 主进程中编写 TypeScript 代码，注册特定于平台的全局快捷键以切换静音状态。
  - [ ] **单元测试:** 为快捷键处理和状态切换逻辑编写测试。

- [ ] **任务 2.3: 连接渲染进程与主进程**
  - [ ] 使用 Electron 的 IPC (Inter-Process Communication) 机制，允许渲染进程（UI）与主进程（系统集成）进行通信。
  - [ ] **单元测试:** 为 IPC 通信模块编写单元测试。

- [ ] **任务 2.4: 实现UI状态逻辑**
  - [ ] 在 React 组件中编写 TypeScript 代码来管理UI的内部状态。
  - [ ] 监听来自主进程的事件以更新UI。
  - [ ] **单元测试:** 编写测试以验证UI状态能否根据事件正确更新。

- [ ] **审批节点 2: 用户审查并批准客户端交互（托盘/菜单栏图标、全局快捷键）。**

---

### 阶段三：服务端与网络实现

**目标：** 构建服务端，实现网络协议，并开启实时音频流。

- [ ] **任务 3.1: 创建服务端项目**
  - [ ] 初始化一个名为 `teamtalk-server` 的、独立的 Node.js + TypeScript 项目。

- [ ] **任务 3.2: 实现核心服务端逻辑**
  - [ ] 使用 Node.js 的 `dgram` 模块设置一个 UDP 套接字监听器。
  - [ ] 实现管理已连接客户端IP地址列表的逻辑。
  - [ ] 实现音频包转发循环（从一个客户端接收，然后广播给所有其他客户端）。
  - [ ] **单元测试:** 为连接管理和音频转发逻辑编写单元测试。

- [ ] **任务 3.3: 实现客户端-服务端连接**
  - [ ] 在 Electron 主进程中，实现与 Node.js 服务端的 UDP 通信逻辑。
  - [ ] 在渲染进程中，通过 IPC 调用主进程的连接逻辑。
  - [ ] 根据从服务器获取的信息，更新“在线”用户计数。
  - [ ] **单元测试:** 为客户端连接模块编写单元测试。

- [ ] **任务 3.4: 集成音频管道**
  - [ ] 在 Node.js 服务端集成 Opus 编解码库（可能需要 C++ 插件）。
  - [ ] 在 Electron 渲染进程中，使用 Web Audio API 捕获麦克风音频。
  - [ ] 在渲染进程中，将捕获的音频数据通过 IPC 发送给主进程。
  - [ ] 在主进程中，使用 Opus 编解码库对音频进行编码/解码，并通过 UDP 发送/接收。
  - [ ] 在渲染进程中，实现一个抖动缓冲（Jitter Buffer）来处理网络波动。
  - [ ] 在渲染进程中，将解码后的音频播放到扬声器。
  - [ ] **单元测试:** 为音频管道中的核心功能（如编码/解码辅助函数）编写单元测试。

- [ ] **任务 3.5: 最终测试与交付**
  - [ ] 在 Linux 机器上运行服务端，在 Windows 和 macOS 上运行客户端，进行端到端测试。
  - [ ] 使用 `electron-builder` 或 `electron-packager` 构建最终的可分发应用程序包。
