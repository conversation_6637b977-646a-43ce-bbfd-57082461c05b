# 架构设计文档

## 1. 综述 (Overview)

本文档定义了 "TeamTalk" 语音聊天工具的技术架构。

该项目旨在创建一个轻量级、低资源占用、跨平台的语音通信解决方案，核心设计理念是**完全匿名**和**免注册**。整体架构采用客户端/服务端 (C/S) 模型，优先保证性能、稳定性和易于部署。

## 2. 技术选型 (Technology Stack)

| 组件 | 技术/框架 | 语言 | 主要原因 |
| :--- | :--- | :--- | :--- |
| **客户端应用** | **Electron** | TypeScript | 语言统一、Web技术栈、开发效率高 |
| **客户端界面** | **React + TypeScript** | TypeScript | 现代UI开发、组件化、类型安全 |
| **服务端应用** | **Node.js** | TypeScript | 语言统一、异步I/O、生态成熟 |
| **音频编解码** | **Opus** | C/Rust | 高音质、低延迟、网络适应性强 (行业标准) |
| **网络传输** | **UDP** | - | 实时通信首选，速度快，延迟低 |

## 3. 组件设计 (Component Design)

### 3.1. 客户端 (Client)

客户端负责提供用户界面、处理用户输入、与系统集成以及核心的音频收发。

*   **框架 (Framework): Electron**
    *   使用 Chromium 渲染引擎和 Node.js 运行时，允许使用 Web 技术（HTML, CSS, JavaScript/TypeScript）构建桌面应用。
    *   **优点**: 语言统一，前端开发经验可复用，生态系统庞大。
    *   **缺点**: 相较于原生应用，资源占用（内存、CPU）和打包体积通常较大。

*   **用户界面 (User Interface): React + TypeScript**
    *   UI 逻辑将通过 React 组件进行管理，实现响应式界面。
    *   TypeScript 提供类型安全，减少运行时错误。
    *   界面将包含：服务器地址输入框、连接/断开按钮、在线人数显示、麦克风/音频静音状态图标。

*   **核心功能实现 (Core Feature Implementation)**
    *   **系统集成**: 通过 Electron API 在 Windows 的系统托盘或 macOS 的菜单栏创建图标。点击图标可显示/隐藏主界面。
    *   **全局快捷键**: 通过 Electron API 注册平台特定的全局快捷键 (`Ctrl+Alt` / `Command+Option`)，确保在任何应用中都能响应。

### 3.2. 服务端 (Server)

服务端是一个无UI的命令行程序，设计为在 Linux 服务器上 24/7 运行。

*   **框架 (Framework): Node.js + TypeScript**
    *   使用 Node.js 作为运行时，结合 TypeScript 提供类型安全。
    *   利用 Node.js 的异步非阻塞 I/O 模型处理并发连接。
    *   **优点**: 与客户端语言统一，开发效率高，生态系统成熟。
    *   **缺点**: 在 CPU 密集型任务上，性能可能不如 Rust。

*   **核心逻辑 (Core Logic)**
    *   **连接管理**: 监听一个 UDP 端口，维护一个当前所有已连接客户端的地址列表。
    *   **音频转发**: 作为音频数据包的中继。当从一个客户端收到 UDP 音频包时，它会立即将该包复制并转发给地址列表中的所有其他客户端。
    *   **房间管理**: 实现“单一全局房间”逻辑。当第一个用户连接时创建会话，当最后一个用户断开时销毁会话，释放资源。

## 4. 网络与音频 (Networking and Audio)

*   **网络协议 (Network Protocol): UDP**
    *   选择 UDP 是为了最大限度地降低语音传输延迟。与 TCP 不同，UDP 不保证数据包的顺序或送达，这对于实时音频是可接受的。丢失的包只会导致瞬间的音频中断，而不是长时间的延迟。
    *   客户端将实现一个**抖动缓冲 (Jitter Buffer)** 来处理网络抖动，平滑传入的音频流。

*   **音频处理流程 (Audio Handling Flow)**
    1.  **客户端A**: 从麦克风捕获原始音频数据。
    2.  **编码**: 使用 **Opus** 编码器将原始音频压缩成高效的音频包。
    3.  **发送**: 将 Opus 包通过 UDP 发送到服务器。
    4.  **服务端**: 接收到来自客户端A的UDP包，并立即将其广播给所有其他连接的客户端。
    5.  **客户端B**: 接收到来自服务器的UDP包，放入抖动缓冲中。
    6.  **解码**: 从缓冲中取出数据包，使用 **Opus** 解码器将其还原为原始音频数据。
    7.  **播放**: 将解码后的音频数据输送到扬声器播放。

## 5. 部署 (Deployment)

*   **客户端**: 使用 `electron-builder` 等工具进行打包，生成对应平台的可分发安装包（如 `.exe`, `.dmg`）。
*   **服务端**: 在开发机上（或目标服务器上）运行 `tsc && node dist/index.js` 或使用 `pm2` 等进程管理器运行编译后的 JavaScript 文件。
